# Test script for comment functionality (PowerShell)
# This script tests the comment system with proper authentication

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Comment System Test Script" -ForegroundColor Green
    Write-Host "Usage: .\test_comments.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script tests the comment system functionality."
    exit 0
}

$API_URL = "http://localhost:8000"
$ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
$SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJzZXJ2aWNlX3JvbGUiLAogICAgImlzcyI6ICJzdXBhYmFzZS1kZW1vIiwKICAgICJpYXQiOiAxNjQxNzY5MjAwLAogICAgImV4cCI6IDE3OTk1MzU2MDAKfQ.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q"

Write-Host "Testing Comment System..." -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Function to make API requests
function Invoke-ApiRequest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        return Invoke-WebRequest @params
    } catch {
        return $_.Exception.Response
    }
}

# Test 1: Check if tables exist
Write-Host "1. Checking if tables exist..." -ForegroundColor Yellow
$headers = @{
    "apikey" = $SERVICE_KEY
    "Authorization" = "Bearer $SERVICE_KEY"
}

$postsResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/posts?select=*&limit=1" -Headers $headers
if ($postsResponse.StatusCode -eq 200) {
    Write-Host "✓ Posts table accessible" -ForegroundColor Green
} else {
    Write-Host "✗ Posts table not accessible" -ForegroundColor Red
}

$commentsResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/comments?select=*&limit=1" -Headers $headers
if ($commentsResponse.StatusCode -eq 200) {
    Write-Host "✓ Comments table accessible" -ForegroundColor Green
} else {
    Write-Host "✗ Comments table not accessible" -ForegroundColor Red
}

# Test 2: Create a test user (using service key)
Write-Host "`n2. Creating test user..." -ForegroundColor Yellow
$USER_ID = "00000000-0000-0000-0000-000000000001"
$userBody = @{
    id = $USER_ID
    email = "<EMAIL>"
    username = "testuser"
    display_name = "Test User"
} | ConvertTo-Json

$userHeaders = $headers.Clone()
$userHeaders["Content-Type"] = "application/json"

$userResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/users" -Method "POST" -Headers $userHeaders -Body $userBody
if ($userResponse.StatusCode -eq 201 -or $userResponse.StatusCode -eq 409) {
    Write-Host "✓ Test user created" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create test user" -ForegroundColor Red
}

# Test 3: Create a test user profile (using service key)
Write-Host "`n3. Creating test user profile..." -ForegroundColor Yellow
$profileBody = @{
    user_id = $USER_ID
    first_name = "Test"
    last_name = "User"
    country = "Test Country"
    language = "en"
} | ConvertTo-Json

$profileResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/user_profiles" -Method "POST" -Headers $userHeaders -Body $profileBody
if ($profileResponse.StatusCode -eq 201 -or $profileResponse.StatusCode -eq 409) {
    Write-Host "✓ Test user profile created" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create test user profile" -ForegroundColor Red
}

# Test 4: Create a test channel
Write-Host "`n4. Creating test channel..." -ForegroundColor Yellow
$CHANNEL_ID = "00000000-0000-0000-0000-000000000001"
$channelBody = @{
    id = $CHANNEL_ID
    name = "test-channel"
    description = "Test channel for comment testing"
    owner_id = $USER_ID
} | ConvertTo-Json

$channelResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/channels" -Method "POST" -Headers $userHeaders -Body $channelBody
if ($channelResponse.StatusCode -eq 201 -or $channelResponse.StatusCode -eq 409) {
    Write-Host "✓ Test channel created" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create test channel" -ForegroundColor Red
}

# Test 5: Create a test post
Write-Host "`n5. Creating test post..." -ForegroundColor Yellow
$postBody = @{
    user_id = $USER_ID
    content = "This is a test post for comment testing"
    channel_id = $CHANNEL_ID
} | ConvertTo-Json

$postHeaders = $userHeaders.Clone()
$postHeaders["Prefer"] = "return=representation"

$postResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/posts" -Method "POST" -Headers $postHeaders -Body $postBody
if ($postResponse.StatusCode -eq 201) {
    $postData = $postResponse.Content | ConvertFrom-Json
    $POST_ID = $postData[0].id
    Write-Host "✓ Test post created with ID: $POST_ID" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create test post" -ForegroundColor Red
    Write-Host "Response: $($postResponse.Content)" -ForegroundColor Gray
    exit 1
}

# Test 6: Create a test comment
Write-Host "`n6. Creating test comment..." -ForegroundColor Yellow
$commentBody = @{
    post_id = $POST_ID
    user_id = $USER_ID
    content = "This is a test comment"
} | ConvertTo-Json

$commentResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/comments" -Method "POST" -Headers $postHeaders -Body $commentBody
if ($commentResponse.StatusCode -eq 201) {
    $commentData = $commentResponse.Content | ConvertFrom-Json
    $COMMENT_ID = $commentData[0].id
    Write-Host "✓ Test comment created with ID: $COMMENT_ID" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create test comment" -ForegroundColor Red
    Write-Host "Response: $($commentResponse.Content)" -ForegroundColor Gray
}

# Test 7: Check if comment count was updated
Write-Host "`n7. Checking if post comment count was updated..." -ForegroundColor Yellow
$postCheckResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count" -Headers $headers
if ($postCheckResponse.StatusCode -eq 200) {
    $postCheckData = $postCheckResponse.Content | ConvertFrom-Json
    $commentCount = $postCheckData[0].comment_count
    
    if ($commentCount -eq 1) {
        Write-Host "✓ Comment count correctly updated to 1" -ForegroundColor Green
    } else {
        Write-Host "✗ Comment count not updated correctly. Current count: $commentCount" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Failed to check comment count" -ForegroundColor Red
}

# Test 8: Create another comment to test increment
Write-Host "`n8. Creating second comment..." -ForegroundColor Yellow
$comment2Body = @{
    post_id = $POST_ID
    user_id = $USER_ID
    content = "This is a second test comment"
} | ConvertTo-Json

$comment2Response = Invoke-ApiRequest -Url "$API_URL/rest/v1/comments" -Method "POST" -Headers $postHeaders -Body $comment2Body
if ($comment2Response.StatusCode -eq 201) {
    $comment2Data = $comment2Response.Content | ConvertFrom-Json
    $COMMENT2_ID = $comment2Data[0].id
    Write-Host "✓ Second test comment created with ID: $COMMENT2_ID" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to create second test comment" -ForegroundColor Red
}

# Test 9: Check if comment count was incremented
Write-Host "`n9. Checking if comment count was incremented..." -ForegroundColor Yellow
$postCheck2Response = Invoke-ApiRequest -Url "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count" -Headers $headers
if ($postCheck2Response.StatusCode -eq 200) {
    $postCheck2Data = $postCheck2Response.Content | ConvertFrom-Json
    $commentCount2 = $postCheck2Data[0].comment_count
    
    if ($commentCount2 -eq 2) {
        Write-Host "✓ Comment count correctly incremented to 2" -ForegroundColor Green
    } else {
        Write-Host "✗ Comment count not incremented correctly. Current count: $commentCount2" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Failed to check comment count" -ForegroundColor Red
}

# Test 10: Delete a comment and check decrement
Write-Host "`n10. Deleting a comment and checking decrement..." -ForegroundColor Yellow
$deleteResponse = Invoke-ApiRequest -Url "$API_URL/rest/v1/comments?id=eq.$COMMENT2_ID" -Method "DELETE" -Headers $headers
if ($deleteResponse.StatusCode -eq 204) {
    Write-Host "✓ Comment deleted successfully" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to delete comment" -ForegroundColor Red
}

# Check comment count after deletion
$postCheck3Response = Invoke-ApiRequest -Url "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count" -Headers $headers
if ($postCheck3Response.StatusCode -eq 200) {
    $postCheck3Data = $postCheck3Response.Content | ConvertFrom-Json
    $commentCount3 = $postCheck3Data[0].comment_count
    
    if ($commentCount3 -eq 1) {
        Write-Host "✓ Comment count correctly decremented to 1 after deletion" -ForegroundColor Green
    } else {
        Write-Host "✗ Comment count not decremented correctly. Current count: $commentCount3" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Failed to check comment count after deletion" -ForegroundColor Red
}

Write-Host "`n========================" -ForegroundColor Green
Write-Host "Comment system test completed!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
