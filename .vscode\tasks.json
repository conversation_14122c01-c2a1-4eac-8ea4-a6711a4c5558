{"version": "2.0.0", "tasks": [{"label": "launch-medium-phone-emulator", "type": "shell", "command": "flutter", "args": ["emulators", "--launch", "Medium_Phone_API_33"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "flutter-clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "flutter-pub-get", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "flutter-build-android", "type": "shell", "command": "flutter", "args": ["build", "apk", "--debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}