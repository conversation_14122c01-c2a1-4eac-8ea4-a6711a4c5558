import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() {
  group('Post Creation Tests', () {
    late SupabaseClient client;

    setUpAll(() async {
      // Initialize Supabase directly without shared preferences
      client = SupabaseClient(
        'http://gameflex.local:8000',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
      );
    });

    test('Test Database Connection', () async {
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
        expect(response, isA<int>());
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed: $e');
      }
    });

    test('Test Post Creation', () async {
      expect(client, isNotNull);

      // Test basic connectivity
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed');
      }
    });

    test('Test Storage Bucket Access', () async {
      final client = SupabaseService.supabaseClient;

      try {
        final buckets = await client.storage.listBuckets();
        print('✅ Storage connection successful');
        print('Available buckets: ${buckets.map((b) => b.name).join(', ')}');

        final mediaBucket = buckets.where((b) => b.name == 'media').firstOrNull;
        expect(mediaBucket, isNotNull, reason: 'Media bucket should exist');
        print('✅ Media bucket found: ${mediaBucket!.name}');
      } catch (e) {
        print('❌ Storage connection failed: $e');
        fail('Storage connection failed');
      }
    });

    test('Test Authentication', () async {
      final client = SupabaseService.supabaseClient;

      try {
        // Try to sign in with test credentials
        final response = await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        if (response.user != null) {
          print('✅ Authentication successful');
          print('User ID: ${response.user!.id}');
          print('User Email: ${response.user!.email}');
        } else {
          print('⚠️  Authentication returned null user');
        }
      } catch (e) {
        print('❌ Authentication failed: $e');
        // Don't fail the test as auth might not be set up yet
      }
    });

    test('Test Post Creation (Mock)', () async {
      final client = SupabaseService.supabaseClient;

      // Create a test post directly in database
      try {
        final testPost = {
          'user_id':
              '37cc897f-a70a-4d04-9b6e-092d0b861488', // Known dev user ID
          'content': 'Test post created by Flutter test',
          'media_type': 'image',
          'media_url':
              'http://localhost:8000/storage/v1/object/public/media/test.jpg',
          'channel_id': null,
          'like_count': 0,
          'comment_count': 0,
          'is_active': true,
        };

        final response = await client.from('posts').insert(testPost).select();

        if (response.isNotEmpty) {
          print('✅ Post creation successful');
          print('Created post ID: ${response.first['id']}');

          // Clean up - delete the test post
          await client.from('posts').delete().eq('id', response.first['id']);
          print('✅ Test post cleaned up');
        } else {
          print('❌ Post creation returned empty response');
          fail('Post creation failed');
        }
      } catch (e) {
        print('❌ Post creation failed: $e');
        fail('Post creation failed: $e');
      }
    });

    test('Test Upload Service Configuration', () {
      // Test that upload service is properly configured
      expect(uploadService, isNotNull);
      print('✅ Upload service initialized');

      // Test Supabase client access
      final client = SupabaseService.supabaseClient;
      expect(client, isNotNull);
      print('✅ Supabase client accessible from upload service');
    });
  });
}

/// Helper function to run all tests and print results
void runPostCreationTests() async {
  print('🧪 Running Post Creation Tests...\n');

  try {
    await SupabaseService.initialize();
    print('✅ Supabase initialized\n');

    final uploadService = UploadService();
    final client = SupabaseService.supabaseClient;

    // Test 1: Database Connection
    print('📊 Test 1: Database Connection');
    try {
      final response = await client.from('posts').select('count').count();
      print('✅ Database connection successful. Post count: $response\n');
    } catch (e) {
      print('❌ Database connection failed: $e\n');
    }

    // Test 2: Storage Access
    print('🪣 Test 2: Storage Access');
    try {
      final buckets = await client.storage.listBuckets();
      print(
        '✅ Storage accessible. Buckets: ${buckets.map((b) => b.name).join(', ')}\n',
      );
    } catch (e) {
      print('❌ Storage access failed: $e\n');
    }

    // Test 3: Post Creation
    print('📝 Test 3: Post Creation');
    try {
      final testPost = {
        'user_id': '37cc897f-a70a-4d04-9b6e-092d0b861488',
        'content': 'Test post from Flutter test function',
        'media_type': 'image',
        'channel_id': null,
      };

      final response = await client.from('posts').insert(testPost).select();
      if (response.isNotEmpty) {
        print('✅ Post creation successful! ID: ${response.first['id']}');

        // Clean up
        await client.from('posts').delete().eq('id', response.first['id']);
        print('✅ Test post cleaned up\n');
      }
    } catch (e) {
      print('❌ Post creation failed: $e\n');
    }

    print('🏁 Post creation tests completed!');
  } catch (e) {
    print('❌ Test setup failed: $e');
  }
}
