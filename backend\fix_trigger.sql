-- Fix the user profile creation trigger to handle email conflicts
CREATE OR R<PERSON>LACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create profile if user doesn't already exist in public.users (by ID or email)
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = NEW.id OR email = NEW.email) THEN
    INSERT INTO public.users (
      id,
      email,
      username,
      display_name,
      is_verified,
      is_active,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
      COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
      NEW.email_confirmed_at IS NOT NULL,
      true,
      NEW.created_at,
      NEW.updated_at
    ) ON CONFLICT (email) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
