# GameFlex Backend Troubleshooting Script (PowerShell)
# This script helps diagnose common Docker and backend issues

param(
    [switch]$Help,
    [switch]$Detailed
)

if ($Help) {
    Write-Host "GameFlex Backend Troubleshooting" -ForegroundColor Green
    Write-Host "Usage: .\troubleshoot.ps1 [-Detailed]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script checks for common issues with the GameFlex backend setup."
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -Detailed    Show detailed information and logs"
    exit 0
}

Write-Host "🔍 GameFlex Backend Troubleshooting" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

# Check Docker status
Write-Host "1. Checking Docker status..." -ForegroundColor Cyan
try {
    docker info | Out-Null
    Write-Host "   ✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker is not running" -ForegroundColor Red
    Write-Host "   💡 Start Docker Desktop and try again" -ForegroundColor Yellow
    exit 1
}

# Check Docker Compose
Write-Host "2. Checking Docker Compose..." -ForegroundColor Cyan
try {
    docker-compose --version | Out-Null
    Write-Host "   ✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Docker Compose is not available" -ForegroundColor Red
    exit 1
}

# Check environment file
Write-Host "3. Checking environment configuration..." -ForegroundColor Cyan
if (Test-Path ".env") {
    Write-Host "   ✅ .env file exists" -ForegroundColor Green
} else {
    Write-Host "   ❌ .env file missing" -ForegroundColor Red
    Write-Host "   💡 Create .env file from template" -ForegroundColor Yellow
}

# Check port availability
Write-Host "4. Checking port availability..." -ForegroundColor Cyan
$ports = @(2501, 3000, 5432, 8000, 8443, 9000)
foreach ($port in $ports) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "   ⚠️  Port $port is in use" -ForegroundColor Yellow
        } else {
            Write-Host "   ✅ Port $port is available" -ForegroundColor Green
        }
    } catch {
        Write-Host "   ✅ Port $port is available" -ForegroundColor Green
    }
}

# Check container status
Write-Host "5. Checking container status..." -ForegroundColor Cyan
try {
    $containers = docker-compose ps --format json 2>$null | ConvertFrom-Json
    if ($containers) {
        if ($containers -is [array]) {
            Write-Host "   📊 Found $($containers.Count) containers" -ForegroundColor Gray
            foreach ($container in $containers) {
                $status = if ($container.State -eq "running") { "✅" } else { "❌" }
                Write-Host "   $status $($container.Service): $($container.State)" -ForegroundColor Gray
            }
        } else {
            $status = if ($containers.State -eq "running") { "✅" } else { "❌" }
            Write-Host "   $status $($containers.Service): $($containers.State)" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ℹ️  No containers found" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️  Could not check container status" -ForegroundColor Yellow
}

# Check volumes
Write-Host "6. Checking Docker volumes..." -ForegroundColor Cyan
try {
    $volumes = docker volume ls --filter name=supabase --format "{{.Name}}" 2>$null
    if ($volumes) {
        Write-Host "   ✅ Found Supabase volumes" -ForegroundColor Green
        if ($Detailed) {
            $volumes | ForEach-Object { Write-Host "      - $_" -ForegroundColor Gray }
        }
    } else {
        Write-Host "   ℹ️  No Supabase volumes found" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️  Could not check volumes" -ForegroundColor Yellow
}

# Show recent logs if detailed
if ($Detailed) {
    Write-Host ""
    Write-Host "7. Recent container logs..." -ForegroundColor Cyan
    try {
        Write-Host "   📋 Database logs (last 10 lines):" -ForegroundColor Gray
        docker-compose logs --tail=10 db 2>$null
        Write-Host ""
        Write-Host "   📋 Kong logs (last 10 lines):" -ForegroundColor Gray
        docker-compose logs --tail=10 kong 2>$null
    } catch {
        Write-Host "   ⚠️  Could not retrieve logs" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🔧 Common Solutions:" -ForegroundColor Magenta
Write-Host "   • Port conflicts: Change ports in docker-compose.yml" -ForegroundColor Gray
Write-Host "   • Permission issues: Run Docker Desktop as Administrator" -ForegroundColor Gray
Write-Host "   • Container issues: docker-compose down && docker-compose up -d" -ForegroundColor Gray
Write-Host "   • Reset everything: .\stop.ps1 && .\start.ps1" -ForegroundColor Gray
Write-Host ""
